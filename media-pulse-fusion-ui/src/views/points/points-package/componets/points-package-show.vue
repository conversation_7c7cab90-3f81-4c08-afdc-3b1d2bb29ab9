<script setup lang="ts">
import { ref, onMounted, computed } from 'vue';
import { NCard, NGrid, NGridItem, NSpin, NTag, NButton, NIcon, useMessage } from 'naive-ui';
import { fetchGetPointsPackageList } from '@/service/api/points/points-package';

defineOptions({
  name: 'PointsPackageShow'
});

interface PointsPackage {
  id: number;
  packageName: string;
  packageDesc: string;
  pointsAmount: number;
  originalPrice: number;
  salePrice: number;
  discountRate: number;
  bonusPoints: number;
  packageType: string;
  isHot: string;
  sortOrder: number;
  startTime: string;
  endTime: string;
  status: string;
  remark: string;
}

const message = useMessage();
const loading = ref(false);
const packageList = ref<PointsPackage[]>([]);
const scrollContainer = ref<HTMLElement>();
const showScrollIndicator = ref(false);
const scrollProgress = ref(0);

// 计算属性：显示的套餐（显示所有套餐）
const displayPackages = computed(() => {
  return packageList.value;
});

// 获取套餐列表
async function getPackageList() {
  loading.value = true;
  try {
    const { data, error } = await fetchGetPointsPackageList({
      pageNum: 1,
      pageSize: 100,
      status: '0' // 只获取正常状态的套餐
    });

    if (!error && data) {
      // 按显示顺序排序
      packageList.value = data.rows.sort((a: PointsPackage, b: PointsPackage) => (a.sortOrder || 0) - (b.sortOrder || 0));
    }
  } catch (err) {
    message.error('获取套餐列表失败');
    console.error('获取套餐列表失败:', err);
  } finally {
    loading.value = false;
  }
}

// 格式化价格
function formatPrice(price: number) {
  return (price / 100).toFixed(2);
}

// 解析套餐描述中的特性
function parseFeatures(description: string): string[] {
  if (!description) return [];

  // 从描述中提取关键信息
  const features = [];

  // 检查是否包含视频数量信息
  const videoMatch = description.match(/(\d+)个视频/);
  if (videoMatch) {
    features.push(`可观看${videoMatch[1]}个视频`);
  }

  // 检查是否包含单价信息
  const priceMatch = description.match(/单价([\d.]+)元/);
  if (priceMatch) {
    features.push(`单价${priceMatch[1]}元/视频`);
  }

  // 检查是否包含曝光量信息
  const exposureMatch = description.match(/曝光量([\d,]+)/);
  if (exposureMatch) {
    features.push(`预计曝光量${exposureMatch[1]}`);
  }

  // 检查是否包含播放量信息
  const playMatch = description.match(/播放量(\d+)/);
  if (playMatch) {
    features.push(`基础播放量${playMatch[1]}/视频`);
  }

  // 如果没有解析到特性，返回默认特性
  if (features.length === 0) {
    features.push('视频观看权限', '基础功能权限', '智能推荐算法');
  }

  return features.slice(0, 4); // 最多显示4个特性
}

// 处理购买
function handlePurchase(packageItem: PointsPackage) {
  message.info(`购买套餐：${packageItem.packageName}`);
  // TODO: 实现购买逻辑
}

onMounted(() => {
  getPackageList();
});
</script>

<template>
  <div class="points-package-show">
    <div class="header-section">
      <h1 class="page-title">选择适合您的套餐</h1>
      <p class="page-subtitle">各种方案满足不同需求，立即升级您的体验之旅</p>
    </div>

    <NSpin :show="loading">
      <div class="package-container">
        <div
          v-for="packageItem in displayPackages"
          :key="packageItem.id"
          class="package-card"
          :class="{
            'hot-package': packageItem.isHot === '1',
            'discount-package': packageItem.packageType === '2',
            'new-user-package': packageItem.packageType === '3',
            'normal-package': packageItem.packageType === '1'
          }"
        >
          <!-- 推荐标签 -->
          <div v-if="packageItem.isHot === '1'" class="recommend-badge">
            推荐
          </div>

          <div class="package-content">
            <!-- 套餐标题 -->
            <div class="package-header">
              <h3 class="package-name">{{ packageItem.packageName }}</h3>
            </div>

            <!-- 价格部分 -->
            <div class="price-section">
              <span class="currency">¥</span>
              <span class="price">{{ formatPrice(packageItem.salePrice) }}</span>
              <span class="period">/月</span>
            </div>

            <!-- 套餐特性列表 -->
            <div class="features-list">
              <div class="feature-item">
                <NIcon class="feature-icon" size="14">
                  <svg viewBox="0 0 24 24" fill="currentColor">
                    <path d="M9 16.17L4.83 12l-1.42 1.41L9 19 21 7l-1.41-1.41z"/>
                  </svg>
                </NIcon>
                <span>积分数量{{ packageItem.pointsAmount?.toLocaleString() }}</span>
              </div>

              <!-- 根据套餐描述解析特性 -->
              <div
                v-for="feature in parseFeatures(packageItem.packageDesc)"
                :key="feature"
                class="feature-item"
              >
                <NIcon class="feature-icon" size="14">
                  <svg viewBox="0 0 24 24" fill="currentColor">
                    <path d="M9 16.17L4.83 12l-1.42 1.41L9 19 21 7l-1.41-1.41z"/>
                  </svg>
                </NIcon>
                <span>{{ feature }}</span>
              </div>

              <!-- 赠送积分 -->
              <div v-if="packageItem.bonusPoints && packageItem.bonusPoints > 0" class="feature-item">
                <NIcon class="feature-icon" size="14">
                  <svg viewBox="0 0 24 24" fill="currentColor">
                    <path d="M9 16.17L4.83 12l-1.42 1.41L9 19 21 7l-1.41-1.41z"/>
                  </svg>
                </NIcon>
                <span>赠送{{ packageItem.bonusPoints?.toLocaleString() }}积分</span>
              </div>

              <!-- 热门套餐额外特性 -->
              <div v-if="packageItem.isHot === '1'" class="feature-item">
                <NIcon class="feature-icon" size="14">
                  <svg viewBox="0 0 24 24" fill="currentColor">
                    <path d="M9 16.17L4.83 12l-1.42 1.41L9 19 21 7l-1.41-1.41z"/>
                  </svg>
                </NIcon>
                <span>优先客服支持</span>
              </div>
            </div>

            <!-- 购买按钮 -->
            <NButton
              block
              size="large"
              class="purchase-btn"
              :class="{
                'hot-btn': packageItem.isHot === '1',
                'discount-btn': packageItem.packageType === '2',
                'new-user-btn': packageItem.packageType === '3',
                'normal-btn': packageItem.packageType === '1'
              }"
              @click="handlePurchase(packageItem)"
            >
              立即购买
            </NButton>
          </div>
        </div>
      </div>
    </NSpin>
  </div>
</template>

<style scoped>
.points-package-show {
  padding: 40px 24px;
  background: #f8fafc;
  height: 100vh;
  overflow-y: scroll;
}

.header-section {
  text-align: center;
  margin-bottom: 48px;
}

.page-title {
  font-size: 24px;
  font-weight: 600;
  color: #1a202c;
  margin: 0 0 8px 0;
}

.page-subtitle {
  font-size: 14px;
  color: #718096;
  margin: 0;
}

.package-container {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 24px;
  max-width: 1200px;
  margin: 0 auto;
  justify-items: center;
}

.package-card {
  position: relative;
  border-radius: 12px;
  overflow: hidden;
  transition: all 0.3s ease;
  background: white;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  width: 280px;
  min-height: 480px;
  display: flex;
  flex-direction: column;
}

.package-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
}

.basic-package {
  border: 1px solid #e2e8f0;
}

.professional-package {
  border: 2px solid #48bb78;
  position: relative;
}

.enterprise-package {
  border: 1px solid #805ad5;
}

.recommend-badge {
  position: absolute;
  top: -1px;
  left: 50%;
  transform: translateX(-50%);
  background: #48bb78;
  color: white;
  padding: 4px 16px;
  border-radius: 0 0 8px 8px;
  font-size: 12px;
  font-weight: 600;
  z-index: 10;
}

.package-content {
  padding: 24px 20px;
  display: flex;
  flex-direction: column;
  height: 100%;
  flex: 1;
}

.package-header {
  text-align: center;
  margin-bottom: 20px;
}

.package-name {
  font-size: 18px;
  font-weight: 600;
  color: #2d3748;
  margin: 0;
}

.price-section {
  display: flex;
  align-items: baseline;
  justify-content: center;
  gap: 2px;
  margin-bottom: 24px;
}

.currency {
  font-size: 16px;
  color: #4a5568;
  font-weight: 500;
}

.price {
  font-size: 32px;
  font-weight: 700;
  color: #2d3748;
}

.period {
  font-size: 14px;
  color: #718096;
  font-weight: 500;
}

.features-list {
  flex: 1;
  margin-bottom: 24px;
  padding: 0;
}

.feature-item {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 0;
  font-size: 13px;
  color: #4a5568;
  line-height: 1.4;
}

.feature-icon {
  color: #48bb78;
  flex-shrink: 0;
}

.purchase-btn {
  height: 44px;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 600;
  transition: all 0.3s ease;
  margin-top: auto;
}

.basic-btn {
  background: #4299e1 !important;
  border-color: #4299e1 !important;
  color: white !important;
}

.basic-btn:hover {
  background: #3182ce !important;
  border-color: #3182ce !important;
}

.professional-btn {
  background: #48bb78 !important;
  border-color: #48bb78 !important;
  color: white !important;
}

.professional-btn:hover {
  background: #38a169 !important;
  border-color: #38a169 !important;
}

.enterprise-btn {
  background: #805ad5 !important;
  border-color: #805ad5 !important;
  color: white !important;
}

.enterprise-btn:hover {
  background: #6b46c1 !important;
  border-color: #6b46c1 !important;
}


</style>
