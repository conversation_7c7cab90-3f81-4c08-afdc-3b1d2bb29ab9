<script setup lang="ts">
import { ref, onMounted } from 'vue';
import { NCard, NGrid, NGridItem, NSpin, NTag, NButton, NIcon, useMessage } from 'naive-ui';
import { fetchGetPointsPackageList } from '@/service/api/points/points-package';

defineOptions({
  name: 'PointsPackageShow'
});

const message = useMessage();
const loading = ref(false);
const packageList = ref<Api.Points.PointsPackage[]>([]);

// 获取套餐列表
async function getPackageList() {
  loading.value = true;
  try {
    const { data, error } = await fetchGetPointsPackageList({
      pageNum: 1,
      pageSize: 100,
      status: '0' // 只获取正常状态的套餐
    });

    if (!error && data) {
      // 按显示顺序排序
      packageList.value = data.rows.sort((a: Api.Points.PointsPackage, b: Api.Points.PointsPackage) => (a.sortOrder || 0) - (b.sortOrder || 0));
    }
  } catch (err) {
    message.error('获取套餐列表失败');
  } finally {
    loading.value = false;
  }
}

// 格式化价格
function formatPrice(price: number) {
  return (price / 100).toFixed(2);
}

// 获取套餐类型标签
function getPackageTypeTag(type: string) {
  const typeMap = {
    '1': { text: '普通套餐', type: 'default' as const },
    '2': { text: '限时优惠', type: 'warning' as const },
    '3': { text: '新用户专享', type: 'success' as const }
  };
  return typeMap[type] || { text: '未知', type: 'default' as const };
}

// 计算折扣信息
function getDiscountInfo(originalPrice: number, salePrice: number) {
  if (originalPrice <= salePrice) return null;
  const discount = Math.round((1 - salePrice / originalPrice) * 100);
  return discount;
}

// 检查是否在有效期内
function isValidPackage(startTime: string, endTime: string) {
  const now = new Date();
  const start = new Date(startTime);
  const end = new Date(endTime);
  return now >= start && now <= end;
}

// 处理购买
function handlePurchase(packageItem: Api.Points.PointsPackage) {
  message.info(`购买套餐：${packageItem.packageName}`);
  // TODO: 实现购买逻辑
}

onMounted(() => {
  getPackageList();
});
</script>

<template>
  <div class="points-package-show">
    <div class="header-section">
      <h2 class="page-title">积分套餐</h2>
      <p class="page-subtitle">选择适合您的积分套餐，享受更多服务</p>
    </div>

    <NSpin :show="loading">
      <NGrid :cols="1" :x-gap="24" :y-gap="24" responsive="screen" item-responsive class="package-grid">
        <NGridItem
          v-for="packageItem in packageList"
          :key="packageItem.id"
          :span="24"
          class="package-item"
        >
          <NCard
            class="package-card"
            :class="{
              'hot-package': packageItem.isHot === '1',
              'new-user-package': packageItem.packageType === '3'
            }"
            hoverable
          >
            <!-- 热门标签 -->
            <div v-if="packageItem.isHot === '1'" class="hot-badge">
              <NIcon size="16">
                <svg viewBox="0 0 24 24" fill="currentColor">
                  <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
                </svg>
              </NIcon>
              热门
            </div>

            <div class="package-content">
              <!-- 左侧信息 -->
              <div class="package-info">
                <div class="package-header">
                  <h3 class="package-name">{{ packageItem.packageName }}</h3>
                  <NTag
                    :type="getPackageTypeTag(packageItem.packageType).type"
                    size="small"
                    class="package-type-tag"
                  >
                    {{ getPackageTypeTag(packageItem.packageType).text }}
                  </NTag>
                </div>

                <p class="package-description">{{ packageItem.packageDesc }}</p>

                <div class="package-details">
                  <div class="detail-item">
                    <span class="detail-label">积分数量：</span>
                    <span class="detail-value points-amount">{{ packageItem.pointsAmount?.toLocaleString() }}</span>
                  </div>

                  <div v-if="packageItem.bonusPoints && packageItem.bonusPoints > 0" class="detail-item">
                    <span class="detail-label">赠送积分：</span>
                    <span class="detail-value bonus-points">+{{ packageItem.bonusPoints?.toLocaleString() }}</span>
                  </div>
                </div>
              </div>

              <!-- 右侧价格和购买 -->
              <div class="package-action">
                <div class="price-section">
                  <div v-if="getDiscountInfo(packageItem.originalPrice, packageItem.salePrice)" class="discount-badge">
                    {{ getDiscountInfo(packageItem.originalPrice, packageItem.salePrice) }}% OFF
                  </div>

                  <div class="price-info">
                    <div v-if="packageItem.originalPrice > packageItem.salePrice" class="original-price">
                      ¥{{ formatPrice(packageItem.originalPrice) }}
                    </div>
                    <div class="sale-price">
                      ¥{{ formatPrice(packageItem.salePrice) }}
                    </div>
                  </div>
                </div>

                <NButton
                  type="primary"
                  size="large"
                  class="purchase-btn"
                  :class="{ 'hot-btn': packageItem.isHot === '1' }"
                  @click="handlePurchase(packageItem)"
                >
                  立即购买
                </NButton>
              </div>
            </div>
          </NCard>
        </NGridItem>
      </NGrid>
    </NSpin>
  </div>
</template>

<style scoped>
.points-package-show {
  padding: 24px;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  min-height: 100vh;
}

.header-section {
  text-align: center;
  margin-bottom: 40px;
}

.page-title {
  font-size: 32px;
  font-weight: 700;
  color: #2c3e50;
  margin: 0 0 12px 0;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.page-subtitle {
  font-size: 16px;
  color: #7f8c8d;
  margin: 0;
}

.package-grid {
  max-width: 1200px;
  margin: 0 auto;
}

.package-card {
  position: relative;
  border-radius: 20px;
  overflow: hidden;
  transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  border: 1px solid rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(20px);
  background: rgba(255, 255, 255, 0.95);
  box-shadow:
    0 8px 32px rgba(0, 0, 0, 0.1),
    0 4px 16px rgba(0, 0, 0, 0.05);
}

.package-card:hover {
  transform: translateY(-8px);
  box-shadow:
    0 20px 40px rgba(0, 0, 0, 0.15),
    0 8px 24px rgba(0, 0, 0, 0.1);
}

.hot-package {
  border: 2px solid #ff6b6b;
  background: linear-gradient(135deg, rgba(255, 107, 107, 0.05) 0%, rgba(255, 255, 255, 0.95) 100%);
}

.new-user-package {
  border: 2px solid #51cf66;
  background: linear-gradient(135deg, rgba(81, 207, 102, 0.05) 0%, rgba(255, 255, 255, 0.95) 100%);
}

.hot-badge {
  position: absolute;
  top: 20px;
  right: 20px;
  background: linear-gradient(135deg, #ff6b6b, #ee5a52);
  color: white;
  padding: 6px 12px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 4px;
  z-index: 10;
  box-shadow: 0 4px 12px rgba(255, 107, 107, 0.3);
}

.package-content {
  display: flex;
  align-items: center;
  gap: 32px;
  padding: 32px;
}

.package-info {
  flex: 1;
}

.package-header {
  display: flex;
  align-items: center;
  gap: 16px;
  margin-bottom: 16px;
}

.package-name {
  font-size: 24px;
  font-weight: 700;
  color: #2c3e50;
  margin: 0;
}

.package-type-tag {
  font-weight: 600;
}

.package-description {
  font-size: 16px;
  color: #7f8c8d;
  line-height: 1.6;
  margin: 0 0 24px 0;
}

.package-details {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.detail-item {
  display: flex;
  align-items: center;
  gap: 8px;
}

.detail-label {
  font-size: 14px;
  color: #95a5a6;
  font-weight: 500;
}

.detail-value {
  font-size: 16px;
  font-weight: 600;
}

.points-amount {
  color: #3498db;
}

.bonus-points {
  color: #e74c3c;
}

.package-action {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 20px;
}

.price-section {
  position: relative;
  text-align: right;
}

.discount-badge {
  position: absolute;
  top: -12px;
  right: -8px;
  background: linear-gradient(135deg, #e74c3c, #c0392b);
  color: white;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 700;
  transform: rotate(15deg);
  box-shadow: 0 2px 8px rgba(231, 76, 60, 0.3);
}

.price-info {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 4px;
}

.original-price {
  font-size: 16px;
  color: #95a5a6;
  text-decoration: line-through;
  font-weight: 500;
}

.sale-price {
  font-size: 32px;
  font-weight: 700;
  color: #e74c3c;
}

.purchase-btn {
  min-width: 140px;
  height: 48px;
  border-radius: 24px;
  font-size: 16px;
  font-weight: 600;
  transition: all 0.3s ease;
}

.hot-btn {
  background: linear-gradient(135deg, #ff6b6b, #ee5a52);
  border: none;
}

.hot-btn:hover {
  background: linear-gradient(135deg, #ee5a52, #e74c3c);
  transform: translateY(-2px);
  box-shadow: 0 8px 20px rgba(255, 107, 107, 0.4);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .package-content {
    flex-direction: column;
    align-items: stretch;
    gap: 24px;
    padding: 24px;
  }

  .package-action {
    align-items: stretch;
  }

  .price-section {
    text-align: center;
  }

  .price-info {
    align-items: center;
  }

  .purchase-btn {
    width: 100%;
  }

  .page-title {
    font-size: 24px;
  }
}
</style>
