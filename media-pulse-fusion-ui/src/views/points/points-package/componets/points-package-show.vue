<script setup lang="ts">
import { ref, onMounted } from 'vue';
import { NCard, NGrid, NGridItem, NSpin, NTag, NButton, NIcon, useMessage } from 'naive-ui';
import { fetchGetPointsPackageList } from '@/service/api/points/points-package';

defineOptions({
  name: 'PointsPackageShow'
});

interface PointsPackage {
  id: number;
  packageName: string;
  packageDesc: string;
  pointsAmount: number;
  originalPrice: number;
  salePrice: number;
  discountRate: number;
  bonusPoints: number;
  packageType: string;
  isHot: string;
  sortOrder: number;
  startTime: string;
  endTime: string;
  status: string;
  remark: string;
}

const message = useMessage();
const loading = ref(false);
const packageList = ref<PointsPackage[]>([]);
const scrollContainer = ref<HTMLElement>();
const showScrollIndicator = ref(false);
const scrollProgress = ref(0);

// 获取套餐列表
async function getPackageList() {
  loading.value = true;
  try {
    const { data, error } = await fetchGetPointsPackageList({
      pageNum: 1,
      pageSize: 100,
      status: '0' // 只获取正常状态的套餐
    });

    if (!error && data) {
      // 按显示顺序排序
      packageList.value = data.rows.sort((a: PointsPackage, b: PointsPackage) => (a.sortOrder || 0) - (b.sortOrder || 0));
    }
  } catch (err) {
    message.error('获取套餐列表失败');
  } finally {
    loading.value = false;
  }
}

// 格式化价格
function formatPrice(price: number) {
  return (price / 100).toFixed(2);
}

// 获取套餐类型标签
function getPackageTypeTag(type: string) {
  const typeMap = {
    '1': { text: '普通套餐', type: 'default' as const },
    '2': { text: '限时优惠', type: 'warning' as const },
    '3': { text: '新用户专享', type: 'success' as const }
  };
  return typeMap[type] || { text: '未知', type: 'default' as const };
}

// 计算折扣信息
function getDiscountInfo(originalPrice: number, salePrice: number) {
  if (originalPrice <= salePrice) return null;
  const discount = Math.round((1 - salePrice / originalPrice) * 100);
  return discount;
}

// 检查是否在有效期内
function isValidPackage(startTime: string, endTime: string) {
  const now = new Date();
  const start = new Date(startTime);
  const end = new Date(endTime);
  return now >= start && now <= end;
}

// 处理购买
function handlePurchase(packageItem: PointsPackage) {
  message.info(`购买套餐：${packageItem.packageName}`);
  // TODO: 实现购买逻辑
}

onMounted(() => {
  getPackageList();
});
</script>

<template>
  <div class="points-package-show">
    <div class="header-section">
      <h1 class="page-title">选择适合您的套餐</h1>
      <p class="page-subtitle">各种方案满足不同需求，立即升级您的体验之旅</p>
    </div>

    <NSpin :show="loading">
      <div class="package-container">
        <div
          v-for="(packageItem, index) in packageList.slice(0, 3)"
          :key="packageItem.id"
          class="package-card"
          :class="{
            'basic-package': index === 0,
            'professional-package': index === 1,
            'enterprise-package': index === 2
          }"
        >
          <!-- 推荐标签 -->
          <div v-if="index === 1" class="recommend-badge">
            推荐
          </div>

          <div class="package-content">
            <!-- 套餐标题 -->
            <div class="package-header">
              <h3 class="package-name">
                {{ index === 0 ? '基础版' : index === 1 ? '专业版' : '企业版' }}
              </h3>
            </div>

            <!-- 价格部分 -->
            <div class="price-section">
              <span class="currency">¥</span>
              <span class="price">{{ formatPrice(packageItem.salePrice) }}</span>
              <span class="period">/月</span>
            </div>

            <!-- 套餐特性列表 -->
            <div class="features-list">
              <div class="feature-item">
                <NIcon class="feature-icon" size="14">
                  <svg viewBox="0 0 24 24" fill="currentColor">
                    <path d="M9 16.17L4.83 12l-1.42 1.41L9 19 21 7l-1.41-1.41z"/>
                  </svg>
                </NIcon>
                <span>积分数量{{ packageItem.pointsAmount?.toLocaleString() }}</span>
              </div>

              <div class="feature-item">
                <NIcon class="feature-icon" size="14">
                  <svg viewBox="0 0 24 24" fill="currentColor">
                    <path d="M9 16.17L4.83 12l-1.42 1.41L9 19 21 7l-1.41-1.41z"/>
                  </svg>
                </NIcon>
                <span>{{ index === 0 ? '基础功能权限' : index === 1 ? '500G 存储空间' : '系统专属客服' }}</span>
              </div>

              <div class="feature-item">
                <NIcon class="feature-icon" size="14">
                  <svg viewBox="0 0 24 24" fill="currentColor">
                    <path d="M9 16.17L4.83 12l-1.42 1.41L9 19 21 7l-1.41-1.41z"/>
                  </svg>
                </NIcon>
                <span>{{ index === 0 ? '基础播放量' : index === 1 ? '优先客服支持' : '24小时客服支持' }}</span>
              </div>

              <div class="feature-item">
                <NIcon class="feature-icon" size="14">
                  <svg viewBox="0 0 24 24" fill="currentColor">
                    <path d="M9 16.17L4.83 12l-1.42 1.41L9 19 21 7l-1.41-1.41z"/>
                  </svg>
                </NIcon>
                <span>{{ index === 0 ? '基础分析报告' : index === 1 ? '高级分析报告' : '定制化解决方案' }}</span>
              </div>

              <div v-if="index > 0" class="feature-item">
                <NIcon class="feature-icon" size="14">
                  <svg viewBox="0 0 24 24" fill="currentColor">
                    <path d="M9 16.17L4.83 12l-1.42 1.41L9 19 21 7l-1.41-1.41z"/>
                  </svg>
                </NIcon>
                <span>{{ index === 1 ? '高级功能权限' : '企业级安全保障' }}</span>
              </div>

              <div v-if="index === 2" class="feature-item">
                <NIcon class="feature-icon" size="14">
                  <svg viewBox="0 0 24 24" fill="currentColor">
                    <path d="M9 16.17L4.83 12l-1.42 1.41L9 19 21 7l-1.41-1.41z"/>
                  </svg>
                </NIcon>
                <span>专属客户经理</span>
              </div>
            </div>

            <!-- 购买按钮 -->
            <NButton
              block
              size="large"
              class="purchase-btn"
              :class="{
                'basic-btn': index === 0,
                'professional-btn': index === 1,
                'enterprise-btn': index === 2
              }"
              @click="handlePurchase(packageItem)"
            >
              立即购买
            </NButton>
          </div>
        </div>
      </div>
    </NSpin>
  </div>
</template>

<style scoped>
.points-package-show {
  padding: 40px 24px;
  background: #f8fafc;
  min-height: 100vh;
  overflow-y: auto;
  overflow-x: hidden;
}

.header-section {
  text-align: center;
  margin-bottom: 48px;
}

.page-title {
  font-size: 24px;
  font-weight: 600;
  color: #1a202c;
  margin: 0 0 8px 0;
}

.page-subtitle {
  font-size: 14px;
  color: #718096;
  margin: 0;
}

.package-container {
  display: flex;
  gap: 24px;
  max-width: 900px;
  margin: 0 auto;
  justify-content: center;
}

.package-card {
  position: relative;
  border-radius: 16px;
  overflow: hidden;
  transition: all 0.3s ease;
  border: 1px solid #e2e8f0;
  background: white;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
  height: 100%;
}

.package-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 12px 24px rgba(0, 0, 0, 0.1);
}

.basic-package {
  border: 1px solid #e2e8f0;
}

.professional-package {
  border: 2px solid #48bb78;
  position: relative;
}

.enterprise-package {
  border: 1px solid #805ad5;
}

.recommend-badge {
  position: absolute;
  top: -1px;
  left: 50%;
  transform: translateX(-50%);
  background: #48bb78;
  color: white;
  padding: 6px 20px;
  border-radius: 0 0 12px 12px;
  font-size: 12px;
  font-weight: 600;
  z-index: 10;
}

.package-content {
  padding: 32px 24px;
  display: flex;
  flex-direction: column;
  height: 100%;
}

.package-header {
  text-align: center;
  margin-bottom: 32px;
}

.package-name {
  font-size: 20px;
  font-weight: 600;
  color: #2d3748;
  margin: 0 0 16px 0;
}

.price-section {
  display: flex;
  align-items: baseline;
  justify-content: center;
  gap: 4px;
}

.currency {
  font-size: 18px;
  color: #4a5568;
  font-weight: 500;
}

.price {
  font-size: 36px;
  font-weight: 700;
  color: #2d3748;
}

.period {
  font-size: 16px;
  color: #718096;
  font-weight: 500;
}

.features-list {
  flex: 1;
  margin-bottom: 32px;
  max-height: 300px;
  overflow-y: auto;
  overflow-x: hidden;
  padding-right: 8px;
}

/* 特性列表滚动条样式 */
.features-list::-webkit-scrollbar {
  width: 4px;
}

.features-list::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.05);
  border-radius: 2px;
}

.features-list::-webkit-scrollbar-thumb {
  background: #cbd5e0;
  border-radius: 2px;
  transition: all 0.3s ease;
}

.features-list::-webkit-scrollbar-thumb:hover {
  background: #a0aec0;
}

/* Firefox 滚动条样式 */
.features-list {
  scrollbar-width: thin;
  scrollbar-color: #cbd5e0 rgba(0, 0, 0, 0.05);
}

.feature-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px 0;
  font-size: 14px;
  color: #4a5568;
}

.feature-icon {
  color: #48bb78;
  flex-shrink: 0;
}

.purchase-btn {
  height: 48px;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 600;
  transition: all 0.3s ease;
}

.basic-package .purchase-btn {
  background: #4299e1;
  border-color: #4299e1;
}

.basic-package .purchase-btn:hover {
  background: #3182ce;
  border-color: #3182ce;
}

.professional-package .purchase-btn {
  background: #48bb78;
  border-color: #48bb78;
}

.professional-package .purchase-btn:hover {
  background: #38a169;
  border-color: #38a169;
}

.enterprise-package .purchase-btn {
  background: #805ad5;
  border-color: #805ad5;
}

.enterprise-package .purchase-btn:hover {
  background: #6b46c1;
  border-color: #6b46c1;
}

/* 平滑滚动 */
.points-package-show {
  scroll-behavior: smooth;
  -webkit-overflow-scrolling: touch; /* iOS 平滑滚动 */
}

/* 套餐网格滚动优化 */
.package-grid {
  scroll-behavior: smooth;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .points-package-show {
    padding: 24px 16px;
  }

  .package-content {
    padding: 24px 20px;
  }

  .page-title {
    font-size: 24px;
  }

  .price {
    font-size: 28px;
  }

  /* 移动端滚动条调整 */
  .points-package-show::-webkit-scrollbar {
    width: 6px;
  }

  .features-list {
    max-height: 250px;
    padding-right: 4px;
  }

  .features-list::-webkit-scrollbar {
    width: 3px;
  }
}

@media (max-width: 640px) {
  .package-grid {
    gap: 16px;
  }

  .package-content {
    padding: 20px 16px;
  }

  /* 小屏幕滚动优化 */
  .points-package-show {
    padding: 16px 12px;
  }

  .features-list {
    max-height: 200px;
  }
}

/* 触摸设备滚动优化 */
@media (hover: none) and (pointer: coarse) {
  .points-package-show::-webkit-scrollbar {
    width: 0;
    background: transparent;
  }

  .features-list::-webkit-scrollbar {
    width: 0;
    background: transparent;
  }
}
</style>
