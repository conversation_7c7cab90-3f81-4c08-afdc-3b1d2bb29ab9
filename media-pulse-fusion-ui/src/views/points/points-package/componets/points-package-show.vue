<script setup lang="ts">
import { computed, onMounted, ref } from 'vue';
import { NButton, NCard, NGrid, NGridItem, NIcon, NSpin, NTag, useMessage } from 'naive-ui';
import { fetchGetPointsPackageList } from '@/service/api/points/points-package';
import { fetchGetUserPoints } from '@/service/api/points/points-points';
defineOptions({
  name: 'PointsPackageShow'
});

interface PointsPackage {
  id: number;
  packageName: string;
  packageDesc: string;
  pointsAmount: number;
  originalPrice: number;
  salePrice: number;
  discountRate: number;
  bonusPoints: number;
  packageType: string;
  isHot: string;
  sortOrder: number;
  startTime: string;
  endTime: string;
  status: string;
  remark: string;
}

const message = useMessage();
const loading = ref(false);
const packageList = ref<PointsPackage[]>([]);
const userPoints = ref<number>(0);
const pointsLoading = ref(false);

// 计算属性：主要展示的套餐（前3个卡片）
const mainPackages = computed(() => {
  return packageList.value.slice(0, 3);
});

// 计算属性：其他套餐（剩余的套餐）
const otherPackages = computed(() => {
  return packageList.value.slice(3);
});

// 获取套餐列表
async function getPackageList() {
  loading.value = true;
  try {
    const { data, error } = await fetchGetPointsPackageList({
      pageNum: 1,
      pageSize: 100,
      status: '0' // 只获取正常状态的套餐
    });

    if (!error && data) {
      // 按显示顺序排序
      packageList.value = data.rows.sort(
        (a: PointsPackage, b: PointsPackage) => (a.sortOrder || 0) - (b.sortOrder || 0)
      );
    }
  } catch (err) {
    message.error('获取套餐列表失败');
    console.error('获取套餐列表失败:', err);
  } finally {
    loading.value = false;
  }
}

// 获取用户积分
async function getUserPoints() {
  pointsLoading.value = true;
  try {
    const { data, error } = await fetchGetUserPoints();
    if (!error && data !== undefined) {
      userPoints.value = data;
    }
  } catch (err) {
    message.error('获取用户积分失败');
    console.error('获取用户积分失败:', err);
  } finally {
    pointsLoading.value = false;
  }
}

// 格式化价格 - 直接显示实际价格，不除以100
function formatPrice(price: number) {
  return price.toString();
}


// 处理购买
function handlePurchase(packageItem: PointsPackage) {
  message.info(`购买套餐：${packageItem.packageName}`);
  // TODO: 实现购买逻辑
}

onMounted(() => {
  getPackageList();
  getUserPoints();
});
</script>

<template>
  <div class="points-package-show">

    <!-- 用户积分展示 -->
    <div class="user-points-section">
      <NCard class="points-card" :bordered="false">
        <div class="points-content">
          <div class="points-icon">
            <NIcon size="24" color="#f59e0b">
              <svg viewBox="0 0 24 24" fill="currentColor">
                <path d="M12 2C13.1 2 14 2.9 14 4C14 5.1 13.1 6 12 6C10.9 6 10 5.1 10 4C10 2.9 10.9 2 12 2ZM21 9V7L15 1H5C3.89 1 3 1.89 3 3V21C3 22.11 3.89 23 5 23H19C20.11 23 21 22.11 21 21V9M19 21H5V3H13V9H19Z"/>
              </svg>
            </NIcon>
          </div>
          <div class="points-info">
            <div class="points-label">我的积分</div>
            <div class="points-value">
              <NSpin :show="pointsLoading" size="small">
                <span class="points-number">{{ userPoints.toLocaleString() }}</span>
              </NSpin>
            </div>
          </div>
          <div class="points-action">
            <NButton
              type="primary"
              size="small"
              ghost
              @click="getUserPoints"
              :loading="pointsLoading"
            >
              刷新
            </NButton>
          </div>
        </div>
      </NCard>
    </div>

    <div class="header-section">
      <h1 class="page-title">选择适合您的积分套餐</h1>
      <p class="page-subtitle">一次购买，立即获得积分，享受平台各项服务</p>
    </div>

    <NSpin :show="loading">
      <!-- 主要套餐卡片展示 -->
      <div class="package-container">
        <div
          v-for="(packageItem, index) in mainPackages"
          :key="packageItem.id"
          class="package-card"
          :class="{
            'basic-package': index === 0,
            'professional-package': index === 1,
            'enterprise-package': index === 2
          }"
        >
          <!-- 推荐标签 -->
          <div v-if="index === 1" class="recommend-badge">推荐</div>

          <div class="package-content">
            <!-- 套餐标题 -->
            <div class="package-header">
              <h3 class="package-name">
                {{ index === 0 ? '基础版' : index === 1 ? '专业版' : '企业版' }}
              </h3>
            </div>

            <!-- 价格部分 -->
            <div class="price-section">
              <span class="currency">¥</span>
              <span class="price">{{ formatPrice(packageItem.salePrice) }}</span>
            </div>

            <!-- 套餐特性列表 -->
            <div class="features-list">
              <div class="feature-item">
                <NIcon class="feature-icon" size="14">
                  <svg viewBox="0 0 24 24" fill="currentColor">
                    <path d="M9 16.17L4.83 12l-1.42 1.41L9 19 21 7l-1.41-1.41z" />
                  </svg>
                </NIcon>
                <span>获得{{ packageItem.pointsAmount?.toLocaleString() }}积分</span>
              </div>

              <div class="feature-item">
                <NIcon class="feature-icon" size="14">
                  <svg viewBox="0 0 24 24" fill="currentColor">
                    <path d="M9 16.17L4.83 12l-1.42 1.41L9 19 21 7l-1.41-1.41z" />
                  </svg>
                </NIcon>
                <span>{{ index === 0 ? '基础功能权限' : index === 1 ? '500G 存储空间' : '系统专属客服' }}</span>
              </div>

              <div class="feature-item">
                <NIcon class="feature-icon" size="14">
                  <svg viewBox="0 0 24 24" fill="currentColor">
                    <path d="M9 16.17L4.83 12l-1.42 1.41L9 19 21 7l-1.41-1.41z" />
                  </svg>
                </NIcon>
                <span>{{ index === 0 ? '基础播放量' : index === 1 ? '优先客服支持' : '24小时客服支持' }}</span>
              </div>

              <div class="feature-item">
                <NIcon class="feature-icon" size="14">
                  <svg viewBox="0 0 24 24" fill="currentColor">
                    <path d="M9 16.17L4.83 12l-1.42 1.41L9 19 21 7l-1.41-1.41z" />
                  </svg>
                </NIcon>
                <span>{{ index === 0 ? '基础分析报告' : index === 1 ? '高级分析报告' : '定制化解决方案' }}</span>
              </div>

              <div v-if="index > 0" class="feature-item">
                <NIcon class="feature-icon" size="14">
                  <svg viewBox="0 0 24 24" fill="currentColor">
                    <path d="M9 16.17L4.83 12l-1.42 1.41L9 19 21 7l-1.41-1.41z" />
                  </svg>
                </NIcon>
                <span>{{ index === 1 ? '高级功能权限' : '企业级安全保障' }}</span>
              </div>

              <div v-if="index === 2" class="feature-item">
                <NIcon class="feature-icon" size="14">
                  <svg viewBox="0 0 24 24" fill="currentColor">
                    <path d="M9 16.17L4.83 12l-1.42 1.41L9 19 21 7l-1.41-1.41z" />
                  </svg>
                </NIcon>
                <span>专属客户经理</span>
              </div>
            </div>

            <!-- 购买按钮 -->
            <NButton
              block
              size="large"
              class="purchase-btn"
              :class="{
                'basic-btn': index === 0,
                'professional-btn': index === 1,
                'enterprise-btn': index === 2
              }"
              @click="handlePurchase(packageItem)"
            >
              立即购买
            </NButton>
          </div>
        </div>
      </div>

      <!-- 其他套餐列表展示 -->
      <div v-if="otherPackages.length > 0" class="other-packages-section">
        <div class="section-header">
          <h3 class="section-title">更多积分套餐</h3>
          <p class="section-subtitle">更多积分数量选择，满足不同需求</p>
        </div>

        <div class="other-packages-grid">
          <div v-for="packageItem in otherPackages" :key="packageItem.id" class="package-item-card">
            <div class="package-header">
              <div class="package-title-section">
                <h4 class="package-title">{{ packageItem.packageName }}</h4>
                <div class="package-tags">
                  <NTag v-if="packageItem.isHot === '1'" type="success" size="small">热门</NTag>
                  <NTag v-if="packageItem.packageType === '2'" type="warning" size="small">限时优惠</NTag>
                  <NTag v-if="packageItem.packageType === '3'" type="info" size="small">新用户专享</NTag>
                </div>
              </div>
            </div>

            <div class="package-body">
              <div class="points-display">
                <span class="points-number">{{ packageItem.pointsAmount?.toLocaleString() }}</span>
                <span class="points-label">积分</span>
              </div>

              <div class="price-display">
                <span class="price-symbol">¥</span>
                <span class="price-value">{{ formatPrice(packageItem.salePrice) }}</span>
                <span v-if="packageItem.originalPrice > packageItem.salePrice" class="original-price">
                  ¥{{ formatPrice(packageItem.originalPrice) }}
                </span>
              </div>

              <div v-if="packageItem.packageDesc" class="package-description">
                {{ packageItem.packageDesc }}
              </div>
            </div>

            <div class="package-footer">
              <NButton type="primary" size="large" block class="purchase-button" @click="handlePurchase(packageItem)">
                立即购买
              </NButton>
            </div>
          </div>
        </div>
      </div>
    </NSpin>
  </div>
</template>

<style scoped>
.points-package-show {
  padding: 40px 24px;
  background: #f8fafc;
  height: 100vh;
  overflow-y: scroll;
}

.header-section {
  text-align: center;
  margin-bottom: 48px;
}

.page-title {
  font-size: 24px;
  font-weight: 600;
  color: #1a202c;
  margin: 0 0 8px 0;
}

.page-subtitle {
  font-size: 14px;
  color: #718096;
  margin: 0;
}

.package-container {
  display: flex;
  gap: 24px;
  max-width: 900px;
  margin: 0 auto 60px auto;
  justify-content: center;
}

.package-card {
  position: relative;
  border-radius: 12px;
  overflow: hidden;
  transition: all 0.3s ease;
  background: white;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  width: 280px;
  min-height: 480px;
  display: flex;
  flex-direction: column;
}

.package-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
}

.basic-package {
  border: 1px solid #e2e8f0;
}

.professional-package {
  border: 2px solid #48bb78;
  position: relative;
}

.enterprise-package {
  border: 1px solid #805ad5;
}

.recommend-badge {
  position: absolute;
  top: -1px;
  left: 50%;
  transform: translateX(-50%);
  background: #48bb78;
  color: white;
  padding: 4px 16px;
  border-radius: 0 0 8px 8px;
  font-size: 12px;
  font-weight: 600;
  z-index: 10;
}

.package-content {
  padding: 24px 20px;
  display: flex;
  flex-direction: column;
  height: 100%;
  flex: 1;
}

.package-header {
  text-align: center;
  margin-bottom: 20px;
}

.package-name {
  font-size: 18px;
  font-weight: 600;
  color: #2d3748;
  margin: 0;
}

.price-section {
  display: flex;
  align-items: baseline;
  justify-content: center;
  gap: 2px;
  margin-bottom: 24px;
}

.currency {
  font-size: 16px;
  color: #4a5568;
  font-weight: 500;
}

.price {
  font-size: 32px;
  font-weight: 700;
  color: #2d3748;
}

.features-list {
  flex: 1;
  margin-bottom: 24px;
  padding: 0;
}

.feature-item {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 0;
  font-size: 13px;
  color: #4a5568;
  line-height: 1.4;
}

.feature-icon {
  color: #48bb78;
  flex-shrink: 0;
}

.purchase-btn {
  height: 44px;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 600;
  transition: all 0.3s ease;
  margin-top: auto;
}

.basic-btn {
  background: #4299e1 !important;
  border-color: #4299e1 !important;
  color: white !important;
}

.basic-btn:hover {
  background: #3182ce !important;
  border-color: #3182ce !important;
}

.professional-btn {
  background: #48bb78 !important;
  border-color: #48bb78 !important;
  color: white !important;
}

.professional-btn:hover {
  background: #38a169 !important;
  border-color: #38a169 !important;
}

.enterprise-btn {
  background: #805ad5 !important;
  border-color: #805ad5 !important;
  color: white !important;
}

.enterprise-btn:hover {
  background: #6b46c1 !important;
  border-color: #6b46c1 !important;
}

/* 其他套餐区域样式 */
.other-packages-section {
  margin-top: 60px;
  max-width: 1000px;
  margin-left: auto;
  margin-right: auto;
}

.section-header {
  text-align: center;
  margin-bottom: 40px;
}

.section-title {
  font-size: 20px;
  font-weight: 600;
  color: #1a202c;
  margin: 0 0 8px 0;
}

.section-subtitle {
  font-size: 14px;
  color: #718096;
  margin: 0;
}

.other-packages-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
  gap: 20px;
  max-width: 1000px;
  margin: 0 auto;
}

.package-item-card {
  background: white;
  border-radius: 16px;
  border: 1px solid #e2e8f0;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  transition: all 0.3s ease;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.package-item-card:hover {
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
  transform: translateY(-4px);
  border-color: #cbd5e0;
}

.package-header {
  padding: 20px 20px 0 20px;
}

.package-title-section {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 16px;
}

.package-title {
  font-size: 18px;
  font-weight: 600;
  color: #2d3748;
  margin: 0;
}

.package-tags {
  display: flex;
  gap: 6px;
}

.package-body {
  padding: 0 20px;
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
}

.points-display {
  display: flex;
  align-items: baseline;
  gap: 6px;
  margin-bottom: 12px;
}

.points-number {
  font-size: 28px;
  font-weight: 700;
  color: #2d3748;
}

.points-label {
  font-size: 16px;
  color: #718096;
  font-weight: 500;
}

.price-display {
  display: flex;
  align-items: baseline;
  gap: 4px;
  margin-bottom: 16px;
}

.price-symbol {
  font-size: 18px;
  color: #e53e3e;
  font-weight: 600;
}

.price-value {
  font-size: 24px;
  font-weight: 700;
  color: #e53e3e;
}

.original-price {
  font-size: 16px;
  color: #a0aec0;
  text-decoration: line-through;
  margin-left: 8px;
}

.package-description {
  font-size: 14px;
  color: #718096;
  line-height: 1.5;
  margin-bottom: 20px;
}

.package-footer {
  padding: 0 20px 20px 20px;
}

.purchase-button {
  height: 44px;
  font-size: 14px;
  font-weight: 600;
  border-radius: 8px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .other-packages-grid {
    grid-template-columns: 1fr;
    gap: 16px;
    padding: 0 16px;
  }

  .package-item-card {
    border-radius: 12px;
  }

  .points-number {
    font-size: 24px;
  }

  .price-value {
    font-size: 20px;
  }
}

@media (max-width: 480px) {
  .other-packages-section {
    margin-top: 40px;
  }

  .section-title {
    font-size: 18px;
  }

  .package-header {
    padding: 16px 16px 0 16px;
  }

  .package-body {
    padding: 0 16px;
  }

  .package-footer {
    padding: 0 16px 16px 16px;
  }

  .package-title {
    font-size: 16px;
  }

  .points-number {
    font-size: 22px;
  }

  .price-value {
    font-size: 18px;
  }
}
</style>
