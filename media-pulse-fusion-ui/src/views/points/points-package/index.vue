<script setup lang="tsx">
import { NDivider } from 'naive-ui';
import { fetchBatchDeletePointsPackage, fetchGetPointsPackageList } from '@/service/api/points/points-package';
import { useAppStore } from '@/store/modules/app';
import { useAuth } from '@/hooks/business/auth';
import { useDownload } from '@/hooks/business/download';
import { useTable, useTableOperate } from '@/hooks/common/table';
import { $t } from '@/locales';
import ButtonIcon from '@/components/custom/button-icon.vue';
import PointsPackageOperateDrawer from './modules/points-package-operate-drawer.vue';
import PointsPackageSearch from './modules/points-package-search.vue';
import PointsPackageShow from '@/views/points/points-package/componets/points-package-show.vue';

defineOptions({
  name: 'PointsPackageList'
});

const appStore = useAppStore();
const { download } = useDownload();
const { hasAuth } = useAuth();

const {
  columns,
  columnChecks,
  data,
  getData,
  getDataByPage,
  loading,
  mobilePagination,
  searchParams,
  resetSearchParams
} = useTable({
  apiFn: fetchGetPointsPackageList,
  apiParams: {
    pageNum: 1,
    pageSize: 10,
    // if you want to use the searchParams in Form, you need to define the following properties, and the value is null
    // the value can not be undefined, otherwise the property in Form will not be reactive
    packageName: null,
    packageDesc: null,
    pointsAmount: null,
    originalPrice: null,
    salePrice: null,
    discountRate: null,
    bonusPoints: null,
    packageType: null,
    isHot: null,
    sortOrder: null,
    startTime: null,
    endTime: null,
    status: null,
    params: {}
  },
  columns: () => [
    {
      type: 'selection',
      align: 'center',
      width: 48
    },
    {
      key: 'index',
      title: $t('common.index'),
      align: 'center',
      width: 64
    },
    {
      key: 'id',
      title: '主键ID',
      align: 'center',
      minWidth: 120
    },
    {
      key: 'packageName',
      title: '套餐名称',
      align: 'center',
      minWidth: 120
    },
    {
      key: 'packageDesc',
      title: '套餐描述',
      align: 'center',
      minWidth: 120
    },
    {
      key: 'pointsAmount',
      title: '积分数量',
      align: 'center',
      minWidth: 120
    },
    {
      key: 'originalPrice',
      title: '原价',
      align: 'center',
      minWidth: 120
    },
    {
      key: 'salePrice',
      title: '售价',
      align: 'center',
      minWidth: 120
    },
    {
      key: 'discountRate',
      title: '折扣率(%)',
      align: 'center',
      minWidth: 120
    },
    {
      key: 'bonusPoints',
      title: '赠送积分',
      align: 'center',
      minWidth: 120
    },
    {
      key: 'packageType',
      title: '套餐类型（1普通套餐 2限时优惠 3新用户专享）',
      align: 'center',
      minWidth: 120
    },
    {
      key: 'isHot',
      title: '是否热门（0否 1是）',
      align: 'center',
      minWidth: 120
    },
    {
      key: 'sortOrder',
      title: '显示顺序',
      align: 'center',
      minWidth: 120
    },
    {
      key: 'startTime',
      title: '生效时间',
      align: 'center',
      minWidth: 120
    },
    {
      key: 'endTime',
      title: '失效时间',
      align: 'center',
      minWidth: 120
    },
    {
      key: 'status',
      title: '状态（0正常 1停用）',
      align: 'center',
      minWidth: 120
    },
    {
      key: 'remark',
      title: '备注',
      align: 'center',
      minWidth: 120
    },
    {
      key: 'operate',
      title: $t('common.operate'),
      align: 'center',
      width: 130,
      render: row => {
        const divider = () => {
          if (!hasAuth('points:pointsPackage:edit') || !hasAuth('points:pointsPackage:remove')) {
            return null;
          }
          return <NDivider vertical />;
        };

        const editBtn = () => {
          if (!hasAuth('points:pointsPackage:edit')) {
            return null;
          }
          return (
            <ButtonIcon
              text
              type="primary"
              icon="material-symbols:drive-file-rename-outline-outline"
              tooltipContent={$t('common.edit')}
              onClick={() => edit(row.id!)}
            />
          );
        };

        const deleteBtn = () => {
          if (!hasAuth('points:pointsPackage:remove')) {
            return null;
          }
          return (
            <ButtonIcon
              text
              type="error"
              icon="material-symbols:delete-outline"
              tooltipContent={$t('common.delete')}
              popconfirmContent={$t('common.confirmDelete')}
              onPositiveClick={() => handleDelete(row.id!)}
            />
          );
        };

        return (
          <div class="flex-center gap-8px">
            {editBtn()}
            {divider()}
            {deleteBtn()}
          </div>
        );
      }
    }
  ]
});

const { drawerVisible, operateType, editingData, handleAdd, handleEdit, checkedRowKeys, onBatchDeleted, onDeleted } =
  useTableOperate(data, getData);

async function handleBatchDelete() {
  // request
  const { error } = await fetchBatchDeletePointsPackage(checkedRowKeys.value);
  if (error) return;
  onBatchDeleted();
}

async function handleDelete(id: CommonType.IdType) {
  // request
  const { error } = await fetchBatchDeletePointsPackage([id]);
  if (error) return;
  onDeleted();
}

function edit(id: CommonType.IdType) {
  handleEdit('id', id);
}

function handleExport() {
  download('/points/pointsPackage/export', searchParams, `积分套餐_${new Date().getTime()}.xlsx`);
}
</script>

<template>
  <div class="min-h-500px flex-col-stretch gap-16px overflow-hidden lt-sm:overflow-auto">
<!--    <PointsPackageSearch v-model:model="searchParams" @reset="resetSearchParams" @search="getDataByPage" />-->
<!--    <NCard title="积分套餐列表" :bordered="false" size="small" class="sm:flex-1-hidden card-wrapper">-->
<!--      <template #header-extra>-->
<!--        <TableHeaderOperation-->
<!--          v-model:columns="columnChecks"-->
<!--          :disabled-delete="checkedRowKeys.length === 0"-->
<!--          :loading="loading"-->
<!--          :show-add="hasAuth('points:pointsPackage:add')"-->
<!--          :show-delete="hasAuth('points:pointsPackage:remove')"-->
<!--          :show-export="hasAuth('points:pointsPackage:export')"-->
<!--          @add="handleAdd"-->
<!--          @delete="handleBatchDelete"-->
<!--          @export="handleExport"-->
<!--          @refresh="getData"-->
<!--        />-->
<!--      </template>-->
<!--      <NDataTable-->
<!--        v-model:checked-row-keys="checkedRowKeys"-->
<!--        :columns="columns"-->
<!--        :data="data"-->
<!--        size="small"-->
<!--        :flex-height="!appStore.isMobile"-->
<!--        :scroll-x="962"-->
<!--        :loading="loading"-->
<!--        remote-->
<!--        :row-key="row => row.id"-->
<!--        :pagination="mobilePagination"-->
<!--        class="sm:h-full"-->
<!--      />-->
<!--      <PointsPackageOperateDrawer-->
<!--        v-model:visible="drawerVisible"-->
<!--        :operate-type="operateType"-->
<!--        :row-data="editingData"-->
<!--        @submitted="getDataByPage"-->
<!--      />-->
<!--    </NCard>-->


    <PointsPackageShow/>
  </div>
</template>

<style scoped></style>
